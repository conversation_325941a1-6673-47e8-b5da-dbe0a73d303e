"""
Streamlit Frontend Application for PR Statement Generator.

This application provides a user-friendly interface for generating PR statements
by communicating with the FastAPI backend service.
"""

import streamlit as st
import requests
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
BACKEND_URL = "http://localhost:8000"
API_ENDPOINT = f"{BACKEND_URL}/api/generate-pr-statement"
HEALTH_ENDPOINT = f"{BACKEND_URL}/health"


class APIClient:
    """Client for communicating with the backend API."""
    
    @staticmethod
    def check_backend_health() -> bool:
        """Check if the backend service is healthy."""
        try:
            response = requests.get(HEALTH_ENDPOINT, timeout=5)
            return response.status_code == 200
        except requests.exceptions.RequestException as e:
            logger.error(f"Backend health check failed: {e}")
            return False
    
    @staticmethod
    def generate_pr_statement(topic: str) -> Dict[str, Any]:
        """
        Generate PR statement by calling the backend API.
        
        Args:
            topic: Topic for PR statement generation
            
        Returns:
            API response containing PR statement and status
        """
        try:
            payload = {"topic": topic}
            response = requests.post(
                API_ENDPOINT,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                error_detail = response.json().get("detail", "Unknown error")
                return {
                    "pr_statement": "",
                    "status": "error",
                    "message": f"API Error ({response.status_code}): {error_detail}"
                }
        
        except requests.exceptions.Timeout:
            return {
                "pr_statement": "",
                "status": "error",
                "message": "Request timeout. The backend service may be overloaded."
            }
        except requests.exceptions.ConnectionError:
            return {
                "pr_statement": "",
                "status": "error",
                "message": "Cannot connect to backend service. Please ensure it's running."
            }
        except Exception as e:
            logger.error(f"API request failed: {e}")
            return {
                "pr_statement": "",
                "status": "error",
                "message": f"Unexpected error: {str(e)}"
            }


def main():
    """Main Streamlit application."""
    
    # Page configuration
    st.set_page_config(
        page_title="📝 PR Statement Generator",
        page_icon="📝",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Sidebar with service status
    with st.sidebar:
        st.subheader("🔧 Service Status")
        
        # Check backend health
        if APIClient.check_backend_health():
            st.success("✅ Backend Service: Online")
        else:
            st.error("❌ Backend Service: Offline")
            st.warning("Please start the backend service to use the application.")
        
        st.markdown("---")
        st.subheader("📋 Service Information")
        st.info(f"**Backend URL:** {BACKEND_URL}")
        st.info("**Frontend Port:** 8501")
        
        st.markdown("---")
        st.subheader("🏗️ Architecture")
        st.markdown("""
        **Microservices Architecture:**
        - **Frontend:** Streamlit (UI/UX)
        - **Backend:** FastAPI (Business Logic)
        - **Communication:** HTTP/JSON
        """)
    
    # Main content
    st.title("📝 AI-Powered PR Statement Generator")
    
    st.markdown("""
    Welcome to the AI-powered PR Statement Generator! 🚀

    This application helps you create compelling Public Relations (PR) statements for various topics. 
    Simply provide the topic you want to create a PR statement for, and our AI model will generate 
    a refined PR statement for you.

    **New Microservices Architecture:**
    - **Decoupled Services:** Frontend and backend run independently
    - **Scalable Design:** Each service can be scaled separately
    - **API-First:** RESTful communication between services

    Let's get started! 🎉
    """)
    
    # User input section
    st.subheader("📝 Generate Your PR Statement")
    
    # Topic input
    topic = st.text_input(
        "Enter the Topic for the PR Statement:",
        value="Company's AI-Powered Chatbot Launch",
        help="Provide a clear and specific topic for your PR statement"
    )
    
    # Validation
    if len(topic.strip()) == 0:
        st.warning("⚠️ Please enter a topic before generating the PR statement.")
        return
    
    if len(topic) > 500:
        st.error("❌ Topic is too long. Please keep it under 500 characters.")
        return
    
    # Generate button
    if st.button("🚀 Generate PR Statement", type="primary"):
        
        # Check backend availability
        if not APIClient.check_backend_health():
            st.error("❌ Backend service is not available. Please start the backend service first.")
            st.info("Run: `cd backend && uvicorn main:app --reload` to start the backend service.")
            return
        
        # Generate PR statement
        with st.spinner("🔄 Generating optimized PR statement..."):
            
            # Progress indicator
            progress_placeholder = st.empty()
            progress_placeholder.info("📡 Sending request to backend service...")
            
            # Call API
            result = APIClient.generate_pr_statement(topic)
            
            # Handle response
            if result["status"] == "success":
                progress_placeholder.success("✅ PR statement generated successfully!")
                
                # Display result
                st.subheader("📄 Generated PR Statement:")
                st.markdown(f"""
                <div style="
                    background-color: #f0f2f6;
                    padding: 20px;
                    border-radius: 10px;
                    border-left: 5px solid #1f77b4;
                    margin: 10px 0;
                ">
                    {result['pr_statement']}
                </div>
                """, unsafe_allow_html=True)
                
                # Additional actions
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("📋 Copy to Clipboard"):
                        st.write("Use Ctrl+C to copy the text above")
                
                with col2:
                    if st.button("🔄 Generate Another"):
                        st.rerun()
            
            else:
                progress_placeholder.error("❌ Failed to generate PR statement")
                st.error(f"**Error:** {result.get('message', 'Unknown error occurred')}")
                
                # Troubleshooting tips
                with st.expander("🔧 Troubleshooting Tips"):
                    st.markdown("""
                    **Common Issues:**
                    1. **Backend Service Offline:** Ensure the FastAPI backend is running
                    2. **Network Issues:** Check your internet connection
                    3. **API Key Issues:** Verify GROQ_API_KEY is set in backend environment
                    4. **Rate Limiting:** Wait a moment and try again
                    
                    **Backend Service Commands:**
                    ```bash
                    cd backend
                    pip install -r requirements.txt
                    export GROQ_API_KEY="your-api-key"
                    uvicorn main:app --reload
                    ```
                    """)
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666;">
        <p>🔗 <strong>Powered by:</strong> FastAPI + Streamlit + LangGraph + Groq 🚀</p>
        <p><strong>Architecture:</strong> Microservices | <strong>Communication:</strong> HTTP/JSON</p>
    </div>
    """, unsafe_allow_html=True)


if __name__ == "__main__":
    main()
