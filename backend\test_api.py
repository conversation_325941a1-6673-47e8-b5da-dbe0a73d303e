"""
Simple test script for the PR Statement Generator Backend API.

This script tests the basic functionality of the FastAPI backend service.
"""

import requests
import json
import time
import sys


def test_health_endpoint():
    """Test the health check endpoint."""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check failed: {e}")
        return False


def test_pr_generation():
    """Test the PR statement generation endpoint."""
    try:
        payload = {"topic": "AI-powered chatbot launch"}
        response = requests.post(
            "http://localhost:8000/api/generate-pr-statement",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success" and result.get("pr_statement"):
                print("✅ PR statement generation passed")
                print(f"   Topic: {payload['topic']}")
                print(f"   Generated PR Statement: {result['pr_statement'][:100]}...")
                return True
            else:
                print(f"❌ PR statement generation failed: {result}")
                return False
        else:
            print(f"❌ PR statement generation failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ PR statement generation failed: {e}")
        return False


def test_invalid_input():
    """Test the API with invalid input."""
    try:
        payload = {"topic": ""}  # Empty topic
        response = requests.post(
            "http://localhost:8000/api/generate-pr-statement",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 422:  # Validation error expected
            print("✅ Input validation test passed")
            return True
        else:
            print(f"❌ Input validation test failed with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Input validation test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Testing PR Statement Generator Backend API")
    print("=" * 50)
    
    # Check if backend is running
    print("1. Testing backend availability...")
    if not test_health_endpoint():
        print("\n❌ Backend service is not running!")
        print("Please start the backend service with:")
        print("   cd backend")
        print("   export GROQ_API_KEY='your-api-key'")
        print("   uvicorn main:app --reload")
        sys.exit(1)
    
    print("\n2. Testing PR statement generation...")
    pr_test_passed = test_pr_generation()
    
    print("\n3. Testing input validation...")
    validation_test_passed = test_invalid_input()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Health Check: ✅")
    print(f"   PR Generation: {'✅' if pr_test_passed else '❌'}")
    print(f"   Input Validation: {'✅' if validation_test_passed else '❌'}")
    
    if pr_test_passed and validation_test_passed:
        print("\n🎉 All tests passed! Backend service is working correctly.")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the backend service.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
