# PR Statement Generator - Frontend Service

Streamlit-based frontend application for the PR Statement Generator microservices architecture.

## Overview

This frontend service provides a user-friendly web interface for generating PR statements by communicating with the FastAPI backend service via HTTP API calls.

## Features

- Clean and intuitive Streamlit web interface
- Real-time backend service health monitoring
- HTTP API communication with proper error handling
- Input validation and user feedback
- Responsive design with service status indicators
- Comprehensive error handling and troubleshooting guides

## Architecture

```
┌─────────────────┐    HTTP/JSON    ┌─────────────────┐
│   Streamlit     │ ──────────────► │   FastAPI       │
│   Frontend      │                 │   Backend       │
│   (Port 8501)   │ ◄────────────── │   (Port 8000)   │
└─────────────────┘                 └─────────────────┘
```

## Installation

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Running the Service

### Development Mode
```bash
streamlit run app.py
```

### Custom Configuration
```bash
streamlit run app.py --server.port 8501 --server.address 0.0.0.0
```

The application will be available at:
- **Frontend:** http://localhost:8501

## Configuration

### Backend Service URL
The frontend is configured to communicate with the backend at:
- **Default:** http://localhost:8000

To change the backend URL, modify the `BACKEND_URL` variable in `app.py`.

### Streamlit Configuration
Configuration is stored in `.streamlit/config.toml`:
- **Port:** 8501
- **Address:** 0.0.0.0
- **Theme:** Custom blue theme

## Features

### Service Health Monitoring
- Real-time backend service status checking
- Visual indicators for service availability
- Automatic error handling when backend is offline

### User Interface
- Topic input with validation (1-500 characters)
- Generate button with loading states
- Formatted PR statement display
- Error handling with troubleshooting tips

### Error Handling
The frontend handles various error scenarios:
- Backend service offline
- Network connectivity issues
- API timeout errors
- Invalid responses

## API Communication

### Request Format
```json
POST /api/generate-pr-statement
{
  "topic": "user input topic"
}
```

### Response Handling
```json
{
  "pr_statement": "generated content",
  "status": "success|error",
  "message": "optional message"
}
```

## Dependencies

- **streamlit**: Web application framework
- **requests**: HTTP client for API communication

See `requirements.txt` for specific versions.

## Troubleshooting

### Common Issues

1. **Backend Service Offline**
   - Ensure the FastAPI backend is running on port 8000
   - Check backend service health at http://localhost:8000/health

2. **Connection Errors**
   - Verify network connectivity
   - Check firewall settings
   - Ensure correct backend URL configuration

3. **API Key Issues**
   - Verify GROQ_API_KEY is set in backend environment
   - Check backend logs for authentication errors

### Starting Both Services

1. **Backend Service:**
   ```bash
   cd backend
   export GROQ_API_KEY="your-api-key"
   uvicorn main:app --reload
   ```

2. **Frontend Service:**
   ```bash
   cd frontend
   streamlit run app.py
   ```

## Development

### Code Structure
```
frontend/
├── app.py              # Main Streamlit application
├── requirements.txt    # Python dependencies
├── .streamlit/
│   └── config.toml    # Streamlit configuration
└── README.md          # This file
```

### API Client
The `APIClient` class handles all backend communication:
- Health check functionality
- PR statement generation requests
- Error handling and timeout management
