"""
Simple test script for the PR Statement Generator Frontend.

This script tests the API client functionality used by the Streamlit frontend.
"""

import sys
import os

# Add the frontend directory to the path to import the app module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import APIClient


def test_backend_health():
    """Test backend health check functionality."""
    print("1. Testing backend health check...")
    
    is_healthy = APIClient.check_backend_health()
    
    if is_healthy:
        print("✅ Backend health check passed")
        return True
    else:
        print("❌ Backend health check failed")
        print("   Make sure the backend service is running on http://localhost:8000")
        return False


def test_api_client():
    """Test the API client PR statement generation."""
    print("2. Testing API client PR statement generation...")
    
    test_topic = "Revolutionary AI chatbot technology"
    result = APIClient.generate_pr_statement(test_topic)
    
    if result["status"] == "success" and result["pr_statement"]:
        print("✅ API client test passed")
        print(f"   Topic: {test_topic}")
        print(f"   Generated PR Statement: {result['pr_statement'][:100]}...")
        return True
    else:
        print("❌ API client test failed")
        print(f"   Error: {result.get('message', 'Unknown error')}")
        return False


def test_error_handling():
    """Test error handling when backend is unavailable."""
    print("3. Testing error handling...")
    
    # Temporarily change the backend URL to test error handling
    original_url = APIClient.__dict__.get('BACKEND_URL', 'http://localhost:8000')
    
    # Test with invalid URL
    import app
    app.BACKEND_URL = "http://localhost:9999"  # Non-existent service
    app.API_ENDPOINT = f"{app.BACKEND_URL}/api/generate-pr-statement"
    
    result = APIClient.generate_pr_statement("test topic")
    
    # Restore original URL
    app.BACKEND_URL = original_url
    app.API_ENDPOINT = f"{app.BACKEND_URL}/api/generate-pr-statement"
    
    if result["status"] == "error":
        print("✅ Error handling test passed")
        print(f"   Error message: {result['message']}")
        return True
    else:
        print("❌ Error handling test failed")
        return False


def main():
    """Run all frontend tests."""
    print("🧪 Testing PR Statement Generator Frontend")
    print("=" * 50)
    
    health_passed = test_backend_health()
    
    if not health_passed:
        print("\n⚠️  Backend is not available. Skipping API tests.")
        print("Please start the backend service with:")
        print("   cd backend")
        print("   export GROQ_API_KEY='your-api-key'")
        print("   uvicorn main:app --reload")
        
        # Still test error handling
        print("\n" + "=" * 30)
        error_handling_passed = test_error_handling()
        
        print("\n" + "=" * 50)
        print("📊 Test Results:")
        print(f"   Backend Health: ❌")
        print(f"   API Client: ⏭️  (Skipped)")
        print(f"   Error Handling: {'✅' if error_handling_passed else '❌'}")
        
        return error_handling_passed
    
    api_client_passed = test_api_client()
    error_handling_passed = test_error_handling()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Backend Health: ✅")
    print(f"   API Client: {'✅' if api_client_passed else '❌'}")
    print(f"   Error Handling: {'✅' if error_handling_passed else '❌'}")
    
    if api_client_passed and error_handling_passed:
        print("\n🎉 All frontend tests passed!")
        return True
    else:
        print("\n⚠️  Some frontend tests failed.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
