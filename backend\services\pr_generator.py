"""
PR Statement Generation Service.

This module contains the core business logic for generating PR statements
using LangGraph workflow and Groq LLM.
"""

import os
import logging
from typing import Dict, Any
from langchain_groq import ChatGroq
from langgraph.graph import StateGraph, START, END

from ..models import State, Feedback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PRGeneratorService:
    """Service class for PR statement generation using LangGraph workflow."""
    
    def __init__(self, groq_api_key: str):
        """
        Initialize the PR Generator Service.
        
        Args:
            groq_api_key: API key for Groq LLM service
        """
        self.groq_api_key = groq_api_key
        self._initialize_llm()
        self._build_workflow()
    
    def _initialize_llm(self):
        """Initialize the LLM and evaluator."""
        try:
            # Set environment variable for Groq API
            os.environ["GROQ_API_KEY"] = self.groq_api_key
            
            # Initialize LLM
            self.llm = ChatGroq(model_name="meta-llama/llama-4-scout-17b-16e-instruct")
            
            # Augment LLM with schema for structured output
            self.evaluator = self.llm.with_structured_output(Feedback)
            
            logger.info("LLM initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            raise
    
    def _build_workflow(self):
        """Build the LangGraph workflow for PR statement generation."""
        try:
            # Create state graph
            workflow = StateGraph(State)
            
            # Add nodes
            workflow.add_node("Generate_PR_Statement", self._generate_pr_statement)
            workflow.add_node("Evaluate_PR_Statement", self._evaluate_pr_statement)
            
            # Add edges
            workflow.add_edge(START, "Generate_PR_Statement")
            workflow.add_edge("Generate_PR_Statement", "Evaluate_PR_Statement")
            workflow.add_conditional_edges(
                "Evaluate_PR_Statement", 
                self._route_statement, 
                {"Accepted": END, "Rejected + Feedback": "Generate_PR_Statement"}
            )
            
            # Compile the workflow
            self.graph = workflow.compile()
            
            logger.info("Workflow built successfully")
        except Exception as e:
            logger.error(f"Failed to build workflow: {e}")
            raise
    
    def _generate_pr_statement(self, state: State) -> Dict[str, Any]:
        """Generate a PR statement based on the given topic."""
        try:
            prompt = f"""
            Generate a compelling PR statement for the topic {state['topic']}. 

            - The statement should highlight key benefits, address any potential concerns, and capture the excitement and innovation surrounding the subject. 
            - Ensure that the tone is professional yet engaging, appealing to the target audience while maintaining clarity and impact."""

            if state.get("feedback"):
                response = self.llm.invoke(prompt + f" Also take provided feedback into account: {state['feedback']}")
            else:
                response = self.llm.invoke(prompt)

            logger.info(f"Generated PR statement for topic: {state['topic']}")
            return {"pr_statement": response.content}
        
        except Exception as e:
            logger.error(f"Error generating PR statement: {e}")
            raise
    
    def _evaluate_pr_statement(self, state: State) -> Dict[str, Any]:
        """Evaluate the generated PR statement and provide feedback."""
        try:
            prompt = f"""
            Review the following PR statement: {state['pr_statement']}. 
            
            - Assess its clarity, engagement, and overall effectiveness in capturing the key benefits and addressing potential concerns. 
            - Decide whether the statement is well-formed ('good') or if it requires further refinement ('needs improvement'). 
            - If it needs improvement, please provide concise and actionable feedback on how to enhance the statement."""

            decision = self.evaluator.invoke(prompt)

            logger.info(f"Evaluated PR statement with grade: {decision.grade}")
            return {"grade": decision.grade, "feedback": decision.feedback}
        
        except Exception as e:
            logger.error(f"Error evaluating PR statement: {e}")
            raise
    
    def _route_statement(self, state: State) -> str:
        """Route to the appropriate node based on the evaluation grade."""
        if state["grade"] == "good":
            return "Accepted"
        else:
            return "Rejected + Feedback"
    
    def generate_pr_statement(self, topic: str) -> str:
        """
        Generate a PR statement for the given topic.
        
        Args:
            topic: The topic for PR statement generation
            
        Returns:
            Generated PR statement
            
        Raises:
            Exception: If generation fails
        """
        try:
            logger.info(f"Starting PR statement generation for topic: {topic}")
            
            # Invoke the workflow
            result = self.graph.invoke({"topic": topic})
            
            logger.info("PR statement generation completed successfully")
            return result["pr_statement"]
        
        except Exception as e:
            logger.error(f"Failed to generate PR statement: {e}")
            raise Exception(f"PR statement generation failed: {str(e)}")
