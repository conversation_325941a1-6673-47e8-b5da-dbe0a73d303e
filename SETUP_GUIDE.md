# PR Statement Generator - Setup Guide

Complete setup guide for running the microservices-based PR Statement Generator.

## 📋 Prerequisites

Before setting up the application, ensure you have:

1. **Python 3.8+** installed
2. **Groq API Key** (sign up at [Groq Console](https://console.groq.com/))
3. **Git** for cloning the repository
4. **Terminal/Command Prompt** access

## 🚀 Quick Setup (Recommended)

### Step 1: <PERSON>lone and Navigate

```bash
git clone https://github.com/chaitanyacherukuri/PR-Statement-Generator.git
cd PR-Statement-Generator
```

### Step 2: Set Up Backend Service

```bash
# Navigate to backend directory
cd backend

# Install dependencies
pip install -r requirements.txt

# Set your Groq API key (replace with your actual key)
export GROQ_API_KEY="your-groq-api-key-here"

# Start the backend service
uvicorn main:app --reload
```

The backend will start on: **http://localhost:8000**

### Step 3: Set Up Frontend Service (New Terminal)

```bash
# Navigate to frontend directory (from project root)
cd frontend

# Install dependencies
pip install -r requirements.txt

# Start the frontend service
streamlit run app.py
```

The frontend will start on: **http://localhost:8501**

### Step 4: Test the Application

1. Open your browser to **http://localhost:8501**
2. Enter a topic (e.g., "AI-powered chatbot launch")
3. Click "Generate PR Statement"
4. View your generated PR statement!

## 🔧 Detailed Setup

### Backend Service Setup

1. **Install Dependencies:**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Environment Configuration:**
   ```bash
   # Linux/Mac
   export GROQ_API_KEY="your-groq-api-key-here"
   
   # Windows (PowerShell)
   $env:GROQ_API_KEY="your-groq-api-key-here"
   
   # Windows (Command Prompt)
   set GROQ_API_KEY=your-groq-api-key-here
   ```

3. **Start Backend:**
   ```bash
   # Development mode (auto-reload)
   uvicorn main:app --reload
   
   # Production mode
   uvicorn main:app --host 0.0.0.0 --port 8000
   ```

4. **Verify Backend:**
   - Health Check: http://localhost:8000/health
   - API Documentation: http://localhost:8000/docs
   - Alternative Docs: http://localhost:8000/redoc

### Frontend Service Setup

1. **Install Dependencies:**
   ```bash
   cd frontend
   pip install -r requirements.txt
   ```

2. **Start Frontend:**
   ```bash
   # Default configuration
   streamlit run app.py
   
   # Custom port
   streamlit run app.py --server.port 8502
   ```

3. **Verify Frontend:**
   - Application: http://localhost:8501
   - Check service status in the sidebar

## 🧪 Testing

### Backend Testing

```bash
cd backend
python test_api.py
```

This will test:
- Health check endpoint
- PR statement generation
- Input validation

### Frontend Testing

```bash
cd frontend
python test_frontend.py
```

This will test:
- Backend connectivity
- API client functionality
- Error handling

## 🐛 Troubleshooting

### Common Issues

#### 1. Backend Won't Start

**Error:** `ModuleNotFoundError: No module named 'fastapi'`

**Solution:**
```bash
cd backend
pip install -r requirements.txt
```

#### 2. Groq API Key Issues

**Error:** `ValueError: GROQ_API_KEY environment variable is required`

**Solution:**
```bash
# Make sure to set the API key before starting the backend
export GROQ_API_KEY="your-actual-api-key"
uvicorn main:app --reload
```

#### 3. Frontend Can't Connect to Backend

**Error:** "Backend service is not available"

**Solution:**
1. Ensure backend is running on port 8000
2. Check backend health: http://localhost:8000/health
3. Verify no firewall blocking the connection

#### 4. Port Already in Use

**Error:** `OSError: [Errno 48] Address already in use`

**Solution:**
```bash
# Find process using the port
lsof -i :8000  # or :8501 for frontend

# Kill the process
kill -9 <PID>

# Or use different ports
uvicorn main:app --port 8001  # Backend
streamlit run app.py --server.port 8502  # Frontend
```

### Service Status Checks

#### Backend Health Check
```bash
curl http://localhost:8000/health
```

#### Frontend Accessibility
```bash
curl http://localhost:8501
```

## 📁 Project Structure Reference

```
PR-Statement-Generator/
├── backend/                    # FastAPI Backend Service
│   ├── main.py                # Main FastAPI application
│   ├── models.py              # Pydantic data models
│   ├── services/
│   │   └── pr_generator.py    # Core business logic
│   ├── requirements.txt       # Backend dependencies
│   ├── test_api.py           # Backend tests
│   └── README.md             # Backend documentation
├── frontend/                  # Streamlit Frontend Service
│   ├── app.py                # Main Streamlit application
│   ├── requirements.txt      # Frontend dependencies
│   ├── test_frontend.py      # Frontend tests
│   ├── .streamlit/
│   │   └── config.toml       # Streamlit configuration
│   └── README.md            # Frontend documentation
├── SETUP_GUIDE.md           # This file
└── README.md                # Main project documentation
```

## 🔄 Development Workflow

1. **Start Backend:** `cd backend && uvicorn main:app --reload`
2. **Start Frontend:** `cd frontend && streamlit run app.py`
3. **Make Changes:** Edit code in respective service directories
4. **Test Changes:** Use the test scripts or manual testing
5. **View Logs:** Check terminal outputs for debugging

## 🚀 Next Steps

After successful setup:

1. **Explore API Documentation:** http://localhost:8000/docs
2. **Customize Frontend:** Modify `frontend/app.py`
3. **Extend Backend:** Add new endpoints in `backend/main.py`
4. **Add Features:** Implement additional PR statement types
5. **Deploy Services:** Consider Docker containerization

## 📞 Support

If you encounter issues:

1. Check this troubleshooting guide
2. Review service-specific README files
3. Check the GitHub repository for updates
4. Ensure all prerequisites are met
