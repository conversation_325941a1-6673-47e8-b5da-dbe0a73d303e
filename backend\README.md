# PR Statement Generator - Backend Service

FastAPI-based backend service for generating compelling PR statements using AI.

## Overview

This backend service provides RESTful API endpoints for PR statement generation using:
- **FastAPI** for the web framework
- **LangGraph** for workflow orchestration
- **Groq** for LLM services (Llama 4 Scout)
- **Pydantic** for data validation

## Features

- RESTful API with automatic OpenAPI/Swagger documentation
- Structured workflow using LangGraph for iterative PR statement refinement
- Input validation and sanitization
- Comprehensive error handling with appropriate HTTP status codes
- CORS configuration for cross-origin requests
- Health check endpoint for monitoring
- Logging and monitoring capabilities

## API Endpoints

### Health Check
```
GET /health
```
Returns service health status.

### Generate PR Statement
```
POST /api/generate-pr-statement
Content-Type: application/json

{
  "topic": "Your topic here"
}
```

**Response:**
```json
{
  "pr_statement": "Generated PR statement content",
  "status": "success",
  "message": "PR statement generated successfully"
}
```

### API Documentation
- **Swagger UI:** http://localhost:8000/docs
- **ReDoc:** http://localhost:8000/redoc

## Installation

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```bash
   export GROQ_API_KEY="your-groq-api-key-here"
   ```

## Running the Service

### Development Mode
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Production Mode
```bash
uvicorn main:app --host 0.0.0.0 --port 8000
```

The service will be available at:
- **API:** http://localhost:8000
- **Documentation:** http://localhost:8000/docs

## Configuration

### Environment Variables
- `GROQ_API_KEY`: Required. Your Groq API key for LLM services.

### CORS Configuration
The service is configured to accept requests from:
- http://localhost:8501 (Streamlit default)
- http://127.0.0.1:8501

## Architecture

```
┌─────────────────┐
│   FastAPI App   │
├─────────────────┤
│   API Routes    │
├─────────────────┤
│ PR Generator    │
│   Service       │
├─────────────────┤
│   LangGraph     │
│   Workflow      │
├─────────────────┤
│   Groq LLM      │
└─────────────────┘
```

## Error Handling

The service implements comprehensive error handling:
- **400 Bad Request:** Invalid input data
- **500 Internal Server Error:** Service errors
- **503 Service Unavailable:** Service initialization failures

## Logging

The service uses Python's built-in logging module with INFO level by default. Logs include:
- Request processing information
- Error details and stack traces
- Service initialization status

## Testing

Test the API using curl:
```bash
curl -X POST "http://localhost:8000/api/generate-pr-statement" \
     -H "Content-Type: application/json" \
     -d '{"topic": "AI-powered chatbot launch"}'
```

## Dependencies

See `requirements.txt` for the complete list of dependencies.
